
## 任务拆解环节

生成各阶段开发任务的markdown文档
- 文件明明规范：`[项目名称]-[任务名称].md`
- 存储位置：项目文档目录或知识库
- 这个阶段不需要编码！不需要编码！不需要编码！


### 🏆 核心目标
- **任务精细化分解**：将复杂需求分解为可执行的具体任务
- **成果复用最大化**：充分利用现有代码和组件，避免重复开发
- **架构持续优化**：识别重构机会，提升代码质量和可维护性
- **依赖关系清晰**：明确任务间的依赖关系和执行顺序
- **质量标准统一**：确保所有任务都符合既定的代码质量标准

### 📋 任务规划工作流程

#### 阶段1：现状分析与资产盘点
1. **代码库扫描**：分析现有代码结构、组件和工具函数
2. **功能模块识别**：识别已实现的功能模块和可复用组件
3. **技术债务评估**：发现需要重构的代码和架构问题
4. **依赖关系梳理**：分析模块间的依赖关系和耦合度

#### 阶段2：需求分析与任务分解
1. **需求理解**：深入理解用户需求和业务目标
2. **功能拆分**：将大功能拆分为独立的小功能模块
3. **复用机会识别**：识别可以复用现有代码的部分
4. **新开发需求确定**：明确需要全新开发的功能点

#### 阶段3：任务优先级规划
1. **依赖关系分析**：确定任务间的前置依赖关系
2. **风险评估**：识别高风险任务和技术难点
3. **价值评估**：评估每个任务的业务价值和技术价值
4. **优先级排序**：制定合理的任务执行顺序

#### 阶段4：重构优化任务制定
1. **代码重复检测**：识别重复或相似的代码片段
2. **抽象机会识别**：发现可以抽象为通用组件的代码
3. **性能优化点**：识别性能瓶颈和优化机会
4. **架构改进建议**：提出架构层面的改进方案

#### 阶段5：任务详细规划
1. **任务详细描述**：为每个任务编写详细的实现说明
2. **验收标准制定**：明确每个任务的完成标准
3. **时间估算**：评估每个任务的开发时间
4. **资源分配**：确定任务所需的技术资源和工具
5. **提示词生成**：为每个任务生成专门的 Augment Code 提示词
6. **归档**：将任务计划归档到markdown文档
